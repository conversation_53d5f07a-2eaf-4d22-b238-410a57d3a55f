package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/user-cluster/complaint-service/dto"
	"pxpat-backend/internal/user-cluster/complaint-service/external/service"
	"pxpat-backend/internal/user-cluster/complaint-service/model"
)

// AdminHandler 管理员处理器
type AdminHandler struct {
	complaintService *service.ComplaintService
	identityService  *service.IdentityService
	rightsService    *service.RightsService
}

// NewAdminHandler 创建管理员处理器实例
func NewAdminHandler(
	complaintService *service.ComplaintService,
	identityService *service.IdentityService,
	rightsService *service.RightsService,
) *AdminHandler {
	return &AdminHandler{
		complaintService: complaintService,
		identityService:  identityService,
		rightsService:    rightsService,
	}
}

// ProcessComplaint 处理投诉
// @Summary 处理投诉
// @Description 管理员处理投诉，可以通过、拒绝或要求补充材料
// @Tags 管理员接口-投诉
// @Accept json
// @Produce json
// @Param id path string true "投诉KSUID"
// @Param request body dto.ProcessComplaintRequest true "处理投诉请求"
// @Success 200 {object} dto.ComplaintResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/admin/complaints/{id}/process [post]
func (h *AdminHandler) ProcessComplaint(c *gin.Context) {
	complaintKSUID := c.Param("id")
	if complaintKSUID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "投诉ID不能为空",
		})
		return
	}

	var req dto.ProcessComplaintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取管理员用户KSUID
	adminKSUID := c.GetString("user_ksuid")
	if adminKSUID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权的访问",
		})
		return
	}

	// 处理投诉
	response, err := h.complaintService.ProcessComplaint(c.Request.Context(), complaintKSUID, adminKSUID, &req)
	if err != nil {
		log.Error().Err(err).Str("complaint_ksuid", complaintKSUID).Msg("处理投诉失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "处理投诉失败",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ReviewIdentityVerification 审核身份认证
// @Summary 审核身份认证
// @Description 管理员审核身份认证申请
// @Tags 管理员接口-身份认证
// @Accept json
// @Produce json
// @Param id path string true "身份认证KSUID"
// @Param request body dto.ReviewIdentityRequest true "审核身份认证请求"
// @Success 200 {object} dto.IdentityVerificationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/admin/identity/{id}/review [post]
func (h *AdminHandler) ReviewIdentityVerification(c *gin.Context) {
	verificationKSUID := c.Param("id")
	if verificationKSUID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "身份认证ID不能为空",
		})
		return
	}

	var req dto.ReviewIdentityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取管理员用户KSUID
	adminKSUID := c.GetString("user_ksuid")
	if adminKSUID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权的访问",
		})
		return
	}

	// 审核身份认证
	response, err := h.identityService.ReviewIdentityVerification(c.Request.Context(), verificationKSUID, adminKSUID, &req)
	if err != nil {
		log.Error().Err(err).Str("verification_ksuid", verificationKSUID).Msg("审核身份认证失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "审核身份认证失败",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ReviewRightsVerification 审核权益认证
// @Summary 审核权益认证
// @Description 管理员审核权益认证申请
// @Tags 管理员接口-权益认证
// @Accept json
// @Produce json
// @Param id path string true "权益认证KSUID"
// @Param request body dto.ReviewRightsRequest true "审核权益认证请求"
// @Success 200 {object} dto.RightsVerificationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/admin/rights/{id}/review [post]
func (h *AdminHandler) ReviewRightsVerification(c *gin.Context) {
	rightsKSUID := c.Param("id")
	if rightsKSUID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "权益认证ID不能为空",
		})
		return
	}

	var req dto.ReviewRightsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取管理员用户KSUID
	adminKSUID := c.GetString("user_ksuid")
	if adminKSUID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "未授权的访问",
		})
		return
	}

	// 审核权益认证
	response, err := h.rightsService.ReviewRightsVerification(c.Request.Context(), rightsKSUID, adminKSUID, &req)
	if err != nil {
		log.Error().Err(err).Str("rights_ksuid", rightsKSUID).Msg("审核权益认证失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "审核权益认证失败",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetComplaintList 获取投诉列表（管理员）
// @Summary 获取投诉列表
// @Description 管理员获取投诉列表，支持筛选和分页
// @Tags 管理员接口-投诉
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param status query string false "投诉状态"
// @Param type query string false "投诉类型"
// @Success 200 {object} dto.ComplaintListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/admin/complaints [get]
func (h *AdminHandler) GetComplaintList(c *gin.Context) {
	// 解析查询参数
	page := 1
	pageSize := 20
	status := c.Query("status")
	complaintType := c.Query("type")

	page = parseIntParam(c, "page", 1)
	pageSize = parseIntParam(c, "page_size", 20)

	// 限制页面大小
	if pageSize > 100 {
		pageSize = 100
	}
	if pageSize < 1 {
		pageSize = 20
	}

	// 构建过滤条件
	filters := dto.ComplaintFilters{
		Page:     page,
		PageSize: pageSize,
	}

	if status != "" {
		filters.Status = model.ComplaintStatus(status)
	}

	if complaintType != "" {
		filters.Type = model.ComplaintType(complaintType)
	}

	// 获取投诉列表
	response, err := h.complaintService.GetComplaintsWithFilters(c.Request.Context(), filters)
	if err != nil {
		log.Error().Err(err).Msg("获取投诉列表失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取投诉列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetIdentityVerificationList 获取身份认证列表（管理员）
// @Summary 获取身份认证列表
// @Description 管理员获取身份认证列表，支持筛选和分页
// @Tags 管理员接口-身份认证
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param status query string false "认证状态"
// @Param type query string false "认证类型"
// @Success 200 {object} dto.IdentityVerificationListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/admin/identity/verifications [get]
func (h *AdminHandler) GetIdentityVerificationList(c *gin.Context) {
	// 解析查询参数
	page := 1
	pageSize := 20
	status := c.Query("status")
	identityType := c.Query("type")

	page = parseIntParam(c, "page", 1)
	pageSize = parseIntParam(c, "page_size", 20)

	// 限制页面大小
	if pageSize > 100 {
		pageSize = 100
	}
	if pageSize < 1 {
		pageSize = 20
	}

	// 构建过滤条件
	filters := dto.IdentityFilters{
		Page:     page,
		PageSize: pageSize,
	}

	if status != "" {
		filters.Status = model.IdentityStatus(status)
	}

	if identityType != "" {
		filters.Type = model.IdentityType(identityType)
	}

	// 获取身份认证列表
	response, err := h.identityService.GetIdentityVerificationsWithFilters(c.Request.Context(), filters)
	if err != nil {
		log.Error().Err(err).Msg("获取身份认证列表失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取身份认证列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetRightsVerificationList 获取权益认证列表（管理员）
// @Summary 获取权益认证列表
// @Description 管理员获取权益认证列表，支持筛选和分页
// @Tags 管理员接口-权益认证
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param status query string false "认证状态"
// @Param type query string false "认证类型"
// @Success 200 {object} dto.RightsVerificationListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/admin/rights/verifications [get]
func (h *AdminHandler) GetRightsVerificationList(c *gin.Context) {
	// 解析查询参数
	page := 1
	pageSize := 20
	status := c.Query("status")
	rightsType := c.Query("type")

	page = parseIntParam(c, "page", 1)
	pageSize = parseIntParam(c, "page_size", 20)

	// 限制页面大小
	if pageSize > 100 {
		pageSize = 100
	}
	if pageSize < 1 {
		pageSize = 20
	}

	// 构建过滤条件
	filters := dto.RightsFilters{
		Page:     page,
		PageSize: pageSize,
	}

	if status != "" {
		filters.Status = model.RightsStatus(status)
	}

	if rightsType != "" {
		filters.Type = model.RightsType(rightsType)
	}

	// 获取权益认证列表
	response, err := h.rightsService.GetRightsVerificationsWithFilters(c.Request.Context(), filters)
	if err != nil {
		log.Error().Err(err).Msg("获取权益认证列表失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取权益认证列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetComplaintsWithFilters 获取投诉列表（管理员）
func (h *AdminHandler) GetComplaintsWithFilters(c *gin.Context) {
	// 解析查询参数
	page := parseIntParam(c, "page", 1)
	pageSize := parseIntParam(c, "page_size", 20)

	// 限制页面大小
	if pageSize > 100 {
		pageSize = 100
	}
	if pageSize < 1 {
		pageSize = 20
	}

	// 构建过滤条件
	filters := dto.ComplaintFilters{
		Page:            page,
		PageSize:        pageSize,
		Status:          model.ComplaintStatus(c.Query("status")),
		Type:            model.ComplaintType(c.Query("type")),
		ComplainerKSUID: c.Query("complainer_ksuid"),
		AccusedKSUID:    c.Query("accused_ksuid"),
		ContentKSUID:    c.Query("content_ksuid"),
		StartDate:       c.Query("start_date"),
		EndDate:         c.Query("end_date"),
		SortBy:          c.Query("sort_by"),
		SortOrder:       c.Query("sort_order"),
	}

	// 调用服务
	response, gErr := h.complaintService.GetComplaintsWithFilters(c.Request.Context(), filters)
	if gErr != nil {
		log.Error().Err(gErr).Msg("获取投诉列表失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取投诉列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetIdentityVerificationsWithFilters 获取身份认证列表（管理员）
func (h *AdminHandler) GetIdentityVerificationsWithFilters(c *gin.Context) {
	// 解析查询参数
	page := parseIntParam(c, "page", 1)
	pageSize := parseIntParam(c, "page_size", 20)

	// 限制页面大小
	if pageSize > 100 {
		pageSize = 100
	}
	if pageSize < 1 {
		pageSize = 20
	}

	// 构建过滤条件
	filters := dto.IdentityFilters{
		Page:         page,
		PageSize:     pageSize,
		Status:       model.IdentityStatus(c.Query("status")),
		Type:         model.IdentityType(c.Query("type")),
		UserKSUID:    c.Query("user_ksuid"),
		ReviewerKSUID: c.Query("reviewer_ksuid"),
		StartDate:    c.Query("start_date"),
		EndDate:      c.Query("end_date"),
		SortBy:       c.Query("sort_by"),
		SortOrder:    c.Query("sort_order"),
	}

	// 调用服务
	response, gErr := h.identityService.GetIdentityVerificationsWithFilters(c.Request.Context(), filters)
	if gErr != nil {
		log.Error().Err(gErr).Msg("获取身份认证列表失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取身份认证列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetRightsVerificationsWithFilters 获取权益认证列表（管理员）
func (h *AdminHandler) GetRightsVerificationsWithFilters(c *gin.Context) {
	// 解析查询参数
	page := parseIntParam(c, "page", 1)
	pageSize := parseIntParam(c, "page_size", 20)

	// 限制页面大小
	if pageSize > 100 {
		pageSize = 100
	}
	if pageSize < 1 {
		pageSize = 20
	}

	// 构建过滤条件
	filters := dto.RightsFilters{
		Page:         page,
		PageSize:     pageSize,
		Status:       model.RightsStatus(c.Query("status")),
		Type:         model.RightsType(c.Query("type")),
		UserKSUID:    c.Query("user_ksuid"),
		ReviewerKSUID: c.Query("reviewer_ksuid"),
		StartDate:    c.Query("start_date"),
		EndDate:      c.Query("end_date"),
		SortBy:       c.Query("sort_by"),
		SortOrder:    c.Query("sort_order"),
	}

	// 调用服务
	response, gErr := h.rightsService.GetRightsVerificationsWithFilters(c.Request.Context(), filters)
	if gErr != nil {
		log.Error().Err(gErr).Msg("获取权益认证列表失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取权益认证列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// parseIntParam 解析整数参数
func parseIntParam(c *gin.Context, param string, defaultValue int) int {
	if value := c.Query(param); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
